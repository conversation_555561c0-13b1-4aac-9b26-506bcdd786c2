<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <style>
        html,
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            cursor: crosshair;
            user-select: none;
            overflow: hidden;
        }

        #overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            pointer-events: none;
            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
        }

        #selection {
            position: absolute;
            border: 2px solid #00ff00;
            background-color: transparent;
            display: none;
            pointer-events: none;
        }
    </style>
</head>

<body>
    <div id="overlay"></div>
    <div id="selection"></div>

    <script>
        const selection = document.getElementById('selection');
        const overlay = document.getElementById('overlay');

        let startX = 0, startY = 0, isSelecting = false;
        let currentX = 0, currentY = 0;
        let animationFrameId = null;

        window.addEventListener('mousedown', (e) => {
            startX = e.clientX;
            startY = e.clientY;
            selection.style.display = 'block';
            isSelecting = true;
        });

        window.addEventListener('mousemove', (e) => {
            if (!isSelecting) return;

            currentX = e.clientX;
            currentY = e.clientY;

            if (!animationFrameId) {
                animationFrameId = requestAnimationFrame(updateSelection);
            }
        });

        function updateSelection() {
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);
            const left = Math.min(currentX, startX);
            const top = Math.min(currentY, startY);
            const right = left + width;
            const bottom = top + height;

            Object.assign(selection.style, {
                left: left + 'px',
                top: top + 'px',
                width: width + 'px',
                height: height + 'px'
            });

            overlay.style.clipPath = `
                polygon(
                    0 0, 100% 0, 100% 100%, 0 100%,
                    0 100%, 0 ${top}px,
                    ${left}px ${top}px,
                    ${left}px ${bottom}px,
                    ${right}px ${bottom}px,
                    ${right}px ${top}px,
                    ${left}px ${top}px,
                    0 ${top}px
                )
            `;

            animationFrameId = null;
        }

        window.addEventListener('mouseup', () => {
            isSelecting = false;
            selection.style.display = 'none';
            overlay.style.clipPath = 'none';

        });
    </script>
</body>

</html>