{"name": "app", "version": "1.0.0", "main": "main.js", "build": {"appId": "com.example.ScreenTranslator", "productName": "ScreenTranslator", "directories": {"output": "dist"}, "win": {"target": "nsis"}, "files": ["**/*"]}, "scripts": {"start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"electron": "^37.2.3", "electron-builder": "^26.0.12"}, "dependencies": {"chrome-launcher": "^1.2.0", "express": "^5.1.0", "jimp": "^1.6.0", "node-localstorage": "^3.0.5", "puppeteer-core": "^24.15.0", "screenshot-desktop": "^1.15.1", "uiohook-napi": "^1.5.4"}}